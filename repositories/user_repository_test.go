package repositories

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewUserRepository(t *testing.T) {
	t.Run("Creates repository correctly", func(t *testing.T) {
		repo := NewUserRepository(nil)

		assert.NotNil(t, repo)
		assert.Nil(t, repo.DB) // DB is nil in this test
	})

	t.Run("Creates repository with correct type", func(t *testing.T) {
		repo := NewUserRepository(nil)

		assert.NotNil(t, repo)
		assert.IsType(t, &UserRepository{}, repo)
	})
}

func TestUserRepository_InterfaceCompliance(t *testing.T) {
	t.Run("Repository implements interface", func(t *testing.T) {
		repo := NewUserRepository(nil)

		// Verify that the repository implements the interface
		var _ UserRepositoryInterface = repo

		assert.NotNil(t, repo)
	})

	t.Run("Repository structure validation", func(t *testing.T) {
		repo := NewUserRepository(nil)

		// Verify the repository has the expected structure
		assert.NotNil(t, repo)
		assert.Nil(t, repo.DB) // DB is nil in this test
		assert.IsType(t, &UserRepository{}, repo)
	})
}

// Note: The ValidateUserServiceAccess method requires a database connection to test properly.
// Integration tests would be needed to test the actual database operations.
// For now, we're testing the constructor and ensuring the repository structure is correct.

func TestUserRepository_ValidateUserServiceAccess_Structure(t *testing.T) {
	t.Run("ValidateUserServiceAccess method exists", func(t *testing.T) {
		repo := NewUserRepository(nil)

		// This ensures the ValidateUserServiceAccess method exists with the correct signature
		// We use interface assignment to verify method existence
		var iface UserRepositoryInterface = repo
		assert.NotNil(t, iface)

		// Verify the repository type
		assert.IsType(t, (*UserRepository)(nil), iface)
	})

	t.Run("Query constant is defined", func(t *testing.T) {
		// Verify that the query constant is properly defined
		assert.NotEmpty(t, validateUserServiceAccessQuery)
		assert.Contains(t, validateUserServiceAccessQuery, "SELECT 1")
		assert.Contains(t, validateUserServiceAccessQuery, "FROM users u")
		assert.Contains(t, validateUserServiceAccessQuery, "JOIN organizations o")
		assert.Contains(t, validateUserServiceAccessQuery, "JOIN services s")
		assert.Contains(t, validateUserServiceAccessQuery, "WHERE u.id = @user_id")
		assert.Contains(t, validateUserServiceAccessQuery, "AND s.id = @service_id")
		assert.Contains(t, validateUserServiceAccessQuery, "deactivated_at IS NULL")
	})
}
