package middlewares

import (
	"context"

	"api.appio.so/models"
	"api.appio.so/pkg/roles"
	"github.com/appio-so/go-appioid"
)

// Define context keys
type PlatformKey struct{}
type Svc<PERSON><PERSON><PERSON> struct{}
type AuthSvcID<PERSON>ey struct{}
type Dvc<PERSON><PERSON><PERSON> struct{}
type <PERSON><PERSON><PERSON> struct{}
type UsrID<PERSON>ey struct{}

func GetPlatformFromContext(ctx context.Context) (models.Platform, bool) {
	platform, ok := ctx.Value(PlatformKey{}).(models.Platform)
	if !ok {
		return "", false
	}
	return platform, ok
}

func GetServiceIDFromContext(ctx context.Context) (*appioid.ID, bool) {
	svcID, ok := ctx.Value(SvcIDKey{}).(*appioid.ID)
	if !ok {
		return nil, false
	}
	return svcID, ok
}

func GetDeviceIDFromContext(ctx context.Context) (*appioid.ID, bool) {
	dvcID, ok := ctx.Value(DvcIDKey{}).(*appioid.ID)
	if !ok {
		return nil, false
	}
	return dvcID, ok
}

func GetUserIDFromContext(ctx context.Context) (*appioid.ID, bool) {
	usrID, ok := ctx.Value(UsrIDKey{}).(*appioid.ID)
	if !ok {
		return nil, false
	}
	return usrID, ok
}

func GetAuthServiceIDFromContext(ctx context.Context) (*appioid.ID, bool) {
	authSvcID, ok := ctx.Value(AuthSvcIDKey{}).(*appioid.ID)
	if !ok {
		return nil, false
	}
	return authSvcID, ok
}

func GetRoleFromContext(ctx context.Context) (roles.Role, bool) {
	role, ok := ctx.Value(RoleKey{}).(roles.Role)
	return role, ok
}
