package services

import (
	"context"

	"api.appio.so/pkg"
	"api.appio.so/repositories"
	"github.com/appio-so/go-appioid"
	"go.uber.org/zap"
)

// UserServiceInterface defines the interface for user service operations
type UserServiceInterface interface {
	ValidateUserServiceAccess(ctx context.Context, userID, serviceID *appioid.ID) error
}

type UserService struct {
	repository repositories.UserRepositoryInterface
	logger     *zap.Logger
}

func NewUserService(repository repositories.UserRepositoryInterface, logger *zap.Logger) *UserService {
	return &UserService{
		repository: repository,
		logger:     logger,
	}
}

// ValidateUserServiceAccess validates that the user has access to the specified service
// Returns nil if access is valid, error otherwise
func (s *UserService) ValidateUserServiceAccess(ctx context.Context, userID, serviceID *appioid.ID) error {
	if userID == nil {
		return pkg.ErrInvalidInput
	}
	if serviceID == nil {
		return pkg.ErrInvalidInput
	}

	hasAccess, err := s.repository.ValidateUserServiceAccess(ctx, userID, serviceID)
	if err != nil {
		s.logger.Error("validating user service access", 
			zap.String("user_id", userID.String()), 
			zap.String("service_id", serviceID.String()), 
			zap.Error(err))
		return pkg.ErrInternal
	}

	if !hasAccess {
		s.logger.Info("user does not have access to service", 
			zap.String("user_id", userID.String()), 
			zap.String("service_id", serviceID.String()))
		return pkg.ErrForbidden
	}

	return nil
}
